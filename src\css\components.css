/* ===== COMPONENTS ===== */
/* Reusable component styles */

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--light-text);
}

.btn-primary:hover {
  background-color: #0d1b69;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--light-text);
}

.btn-secondary:hover {
  background-color: #1e88e5;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.fa-camera {
  margin-right: 8px;
}

.fa-folder-open:before {
  margin-right: 8px;
}



/* Action button styles */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 12px 20px;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-btn.primary-btn {
  background-color: var(--primary-color);
  color: var(--light-text);
}

.action-btn.primary-btn:hover {
  background-color: #0d1b69;
  transform: translateY(-1px);
}

.action-btn.secondary-btn {
  background-color: var(--secondary-color);
  color: var(--light-text);
}

.action-btn.secondary-btn:hover {
  background-color: #1e88e5;
  transform: translateY(-1px);
}

.action-btn.danger-btn {
  background-color: var(--error-color);
  color: var(--light-text);
}

.action-btn.danger-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn:disabled:hover {
  transform: none;
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}

.modal-content {
  position: relative;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: var(--primary-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-color);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.modal-close:hover {
  background-color: #f0f0f0;
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-form .form-group {
  margin-bottom: var(--spacing-lg);
}

.modal-form label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-color);
}

.modal-form input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius-md);
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.modal-form input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-lg);
}

/* Warning message */
.warning-message {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.warning-message i {
  color: #f39c12;
  margin-right: var(--spacing-sm);
}

.warning-message h4 {
  color: #d68910;
  margin-bottom: var(--spacing-sm);
}

.warning-message ul {
  margin-left: var(--spacing-lg);
  margin-top: var(--spacing-sm);
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  text-align: center;
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.loading-spinner p {
  color: var(--text-color);
  font-size: 1.1rem;
}

/* Message styles */
.message-container {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 9998;
  max-width: 400px;
}

.message {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  animation: slideIn 0.3s ease;
}

.message.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.message.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.message.info {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.message i {
  margin-right: var(--spacing-sm);
}

.message span {
  flex: 1;
}

.message-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: var(--spacing-sm);
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.message-close:hover {
  opacity: 1;
}

/* ===== COMPONENT RESPONSIVE STYLES ===== */

@media (max-width: 768px) {
  /* Button responsive */
  .btn {
    padding: 12px 20px;
    font-size: 15px;
  }

  .action-btn {
    padding: 10px 16px;
    font-size: 14px;
    gap: var(--spacing-xs);
  }

  /* Modal responsive */
  .modal-content {
    width: 95%;
    max-width: none;
    margin: var(--spacing-sm);
  }

  .modal-header {
    padding: var(--spacing-md);
  }

  .modal-body {
    padding: var(--spacing-md);
  }

  .modal-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .modal-actions .action-btn {
    width: 100%;
    justify-content: center;
  }

  /* Message container responsive */
  .message-container {
    top: 80px;
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    max-width: none;
  }

  .message {
    padding: var(--spacing-sm);
    font-size: 14px;
  }
}

/* Very small mobile devices - 425px and below */
@media (max-width: 425px) {
  /* Button adjustments for very small screens */
  .btn {
    padding: 8px 14px;
    font-size: 13px;
  }

  .action-btn {
    padding: 6px 10px;
    font-size: 12px;
    gap: 4px;
  }

  /* Modal adjustments for very small screens */
  .modal-content {
    width: 98%;
    margin: 4px;
    border-radius: 12px;
  }

  .modal-header {
    padding: 10px;
  }

  .modal-body {
    padding: 10px;
  }

  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }

  .modal-actions .action-btn {
    width: 100%;
    justify-content: center;
  }

  .modal-form input,
  .modal-form textarea {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 8px 12px;
  }

  /* Message container adjustments */
  .message-container {
    top: 70px;
    right: 8px;
    left: 8px;
    max-width: none;
  }

  .message {
    padding: 8px 12px;
    font-size: 13px;
  }

  /* Login button specific adjustments */
  .login-btn {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 8px;
  }
}

@media (max-width: 480px) {
  /* Extra small adjustments */
  .btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .modal-content {
    width: 98%;
    margin: var(--spacing-xs);
  }

  .modal-header {
    padding: var(--spacing-sm);
  }

  .modal-body {
    padding: var(--spacing-sm);
  }

  .modal-form input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
