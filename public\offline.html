<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anevia - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }

        .offline-container {
            max-width: 500px;
            width: 100%;
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .offline-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .offline-features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }

        .offline-features h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #e8eaf6;
        }

        .feature-list {
            list-style: none;
            text-align: left;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            font-size: 1rem;
            opacity: 0.9;
        }

        .feature-list li::before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            backdrop-filter: blur(10px);
        }

        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .network-status {
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .network-status.offline {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
        }

        .network-status.online {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        @media (max-width: 768px) {
            .offline-title {
                font-size: 2rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .offline-features {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            Don't worry! Anevia works offline too. You can still access previously viewed content and use cached features.
        </p>
        
        <div class="offline-features">
            <h3>Available Offline:</h3>
            <ul class="feature-list">
                <li>View previously scanned results</li>
                <li>Access cached scan history</li>
                <li>Browse saved recommendations</li>
                <li>Read FAQ and information</li>
            </ul>
        </div>

        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>

        <div id="network-status" class="network-status offline">
            No internet connection
        </div>
    </div>

    <script>
        // Monitor network status
        function updateNetworkStatus() {
            const statusElement = document.getElementById('network-status');
            if (navigator.onLine) {
                statusElement.textContent = 'Connection restored!';
                statusElement.className = 'network-status online';
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                statusElement.textContent = 'No internet connection';
                statusElement.className = 'network-status offline';
            }
        }

        // Listen for network changes
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);

        // Initial status check
        updateNetworkStatus();
    </script>
</body>
</html>
