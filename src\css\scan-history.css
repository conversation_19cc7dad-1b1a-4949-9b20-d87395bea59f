/* ===== SCAN HISTORY SECTION STYLES ===== */

#scan-history {
  background-color: #ffffff;
  padding-top: 0;
  min-height: 100vh;
}

.scan-history-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 120px var(--spacing-lg) var(--spacing-xl);
}

.scan-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid #e0e0e0;
}

.scan-history-header h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding:  var(--spacing-md);
  background-color: #e0e0e0;
  color: white;
  border: #999;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}


.back-btn:hover {
  background-color: #1e88e5;
  transform: translateY(-2px);
}

/* Scan History Stats */
.scan-history-stats {
  margin-bottom: var(--spacing-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-item {
  background-color: var(--card-bg);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  /* box-shadow: var(--shadow-md); */
  text-align: center;
  transition: transform 0.3s ease;
  border: 1px solid #d2d6db;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 1rem;
  color: var(--text-color);
  font-weight: 500;
}

/* Scan History List */
.scan-history-list {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.scan-list-header,
.chat-list-header {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-lg);
}

.scan-list-header h3,
.chat-list-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.scan-items,
.chat-items {
  padding: 0;
}

.scan-item,
.chat-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.scan-item:hover,
.chat-item:hover {
  background-color: #f8f9fa;
}

.scan-item:last-child,
.chat-item:last-child {
  border-bottom: none;
}

.scan-item-image {
  width: 80px;
  height: 80px;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
}

.scan-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--border-radius-md);
  border: 2px solid #e0e0e0;
}

.scan-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 1.5rem;
}

.view-chat-btn {
  gap: 8px;
}

.scan-item-info {
  flex: 1;
  margin-right: var(--spacing-lg);
}

.scan-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.scan-item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.scan-item-date {
  font-size: 0.9rem;
  color: #666;
}

.scan-item-result {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.scan-item-result.normal {
  color: var(--success-color);
}

.scan-item-result.anemic {
  color: var(--error-color);
}

.scan-item-confidence {
  font-size: 0.9rem;
  color: #666;
}

.scan-item-actions,
.chat-item-actions {
  flex-shrink: 0;
}

/* Chat Item Specific Styles */
.chat-item-icon {
  width: 60px;
  height: 60px;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.chat-item-info {
  flex: 1;
  margin-right: var(--spacing-lg);
}

.chat-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.chat-item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
  max-width: 70%;
  line-height: 1.3;
}

.chat-item-date {
  font-size: 0.85rem;
  color: #666;
  white-space: nowrap;
}

.chat-item-type,
.chat-item-scan {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  font-size: 0.9rem;
  color: #666;
}

.chat-item-type i,
.chat-item-scan i {
  color: var(--secondary-color);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.9rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  border: 1px solid #d2d6db;
  /* box-shadow: var(--shadow-md); */
}

.empty-icon {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.empty-state p {
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Error State */
.error-state {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.error-icon {
  font-size: 4rem;
  color: var(--error-color);
  margin-bottom: var(--spacing-lg);
}

.error-state h3 {
  font-size: 1.5rem;
  color: var(--error-color);
  margin-bottom: var(--spacing-md);
}

.error-state p {
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
}

/* Loading Container */
.loading-container {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .scan-history-container {
    padding: 100px var(--spacing-md) var(--spacing-lg);
  }

  .scan-history-header {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .scan-history-header h1 {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .scan-item,
  .chat-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .scan-item-info,
  .chat-item-info {
    margin-right: 0;
  }

  .scan-item-header,
  .chat-item-header {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .chat-item-title {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .scan-history-container {
    padding: 80px var(--spacing-sm) var(--spacing-md);
  }

  .scan-history-header h1 {
    font-size: 1.8rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-value {
    font-size: 2rem;
  }
}
