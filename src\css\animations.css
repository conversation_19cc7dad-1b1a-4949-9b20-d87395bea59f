/* ===== ANIMATIONS ===== */
/* Keyframes and animation classes */

/* Animation base classes */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--animation-duration) var(--animation-easing);
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

/* Prevent animation flicker */
[data-animation] {
  opacity: 0;
}

[data-animation].animated {
  opacity: 1;
}

/* Ensure bento cards are visible initially */
.bento-card {
  opacity: 1;
}

.bento-card[data-animation] {
  opacity: 0;
}

.bento-card[data-animation].animated {
  opacity: 1;
}

/* Loading animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Scroll animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Enhanced Typing Animation Keyframes */
@keyframes blink-cursor {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

@keyframes cursor-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 0 rgba(41, 122, 254, 0);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 8px rgba(41, 122, 254, 0.6);
  }
}

/* Enhanced statement text typing animation */
.statement-text.typing-animation {
  position: relative;
  overflow: visible;
  min-height: 1.4em; /* Prevent layout shift */
}

.statement-text .typing-cursor {
  display: inline-block;
  width: 3px;
  height: 1.3em;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  margin-left: 4px;
  animation: blink-cursor 1.2s infinite, cursor-glow 2s infinite;
  vertical-align: text-bottom;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.statement-text .typing-cursor.hidden {
  opacity: 0;
  transform: scale(0);
  transition: all 0.5s ease;
}

/* AI Thinking Animation */
.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dot {
  width: 6px;
  height: 6px;
  background-color: #40474f;
  border-radius: 50%;
  animation: thinkingPulse 1.4s infinite ease-in-out;
}

.thinking-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.thinking-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes thinkingPulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
