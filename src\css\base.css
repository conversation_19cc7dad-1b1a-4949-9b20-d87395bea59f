/* ===== BASE STYLES ===== */
/* Reset, base styles, and typography */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.6;
  overflow-x: hidden; /* Prevent horizontal scroll during animations */
}

/* Smooth transitions for all interactive elements */
button,
.btn,
.card,
.team-member,
.feature-card,
.step {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced focus states for accessibility */
button:focus,
.btn:focus,
a:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  [data-animation] {
    opacity: 1 !important;
  }
}

a {
  text-decoration: none;
  color: #ffffff;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--secondary-color);
}

img {
  max-width: 100%;
  height: auto;
}

/* Typography */
.section-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-lg);
  text-align: center;
  color: var(--primary-color);
}

.section-subtitle {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-xl);
  text-align: center;
  color: var(--text-color);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}
