/* ===== FAQ SECTION STYLES ===== */
/* Styles specific to the FAQ page */

#faq {
  background-color: #f3f3f3;
  height: 100vh;
}

.faq-section {
  background-color: #f8f8f8;
  padding-top:36px;
  height: 100vh;
  justify-content: center;
  align-items: center;
}

.faq-container {
  display: flex;
  flex-direction: column;
  max-width: 1186px;
  height: 100%;
  margin: 0 auto;
  justify-content: center;
  align-items: center;

}

.faq-categories {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.faq-category {
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: #ffffff;
  border-radius: var(--border-radius-lg);
  color: #646464;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid #d2d6db;
  font-weight: 500;
  /* box-shadow: var(--shadow-sm); */
  position: relative;
  overflow: hidden;
}

.faq-category::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.faq-category:hover::before {
  left: 100%;
}

.faq-category:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.faq-category.active {
  background-color: var(--secondary-color);
  color: var(--light-text);
  border-color: var(--secondary-color);
  box-shadow: var(--shadow-md);
}

.accordion {
  width: 100%;
  margin-bottom: var(--spacing-lg);
}

.accordion-item {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  border: 1px solid rgba(26, 35, 126, 0.1);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.accordion-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.accordion-header {
  padding: var(--spacing-lg);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background-color: var(--card-bg);
}

/* Header hover effects are now handled by JavaScript */

.accordion-title::before {
  font-weight: 800;
  color: #1a1a1a;
}

.accordion-title::active {
  font-weight: 800;
  color: var(--primary-color);
}


.accordion-icon {
  color: var(--primary-color);
  transition: transform 0.3s ease;
}

.accordion-item.active .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background-color: var(--card-bg);
}

.accordion-item.active .accordion-content {
  max-height: 1000px; /* Will be set dynamically by JavaScript */
}

.accordion-body {
  padding: 16px 32px;
  color: var(--text-color);
  text-align: left;
  justify-content: center;
  line-height: 1.7;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.accordion-item.active .accordion-body {
  opacity: 1;
  transform: translateY(0);
}

/* ===== FAQ RESPONSIVE STYLES ===== */
@media (max-width: 1024px) {
  .faq-container {
    max-width: 900px;
    padding: 0 var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  #faq {
    height: auto;
    min-height: 100vh;
  }

  .faq-section {
    height: auto;
    min-height: 100vh;
    padding: var(--spacing-xl) 0;
  }

  .faq-container {
    max-width: 100%;
    padding: 0 var(--spacing-md);
    height: auto;
  }

  .faq-categories {
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  .faq-category {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 14px;
    min-width: auto;
    flex: none;
  }

  .accordion-header {
    padding: var(--spacing-md);
    font-size: 16px;
  }

  .accordion-body {
    padding: var(--spacing-md);
    font-size: 14px;
    line-height: 1.6;
  }
}

@media (max-width: 480px) {
  .faq-section {
    padding: var(--spacing-lg) 0;
  }

  .faq-container {
    padding: 0 var(--spacing-sm);
  }

  .faq-categories {
    gap: var(--spacing-xs);
  }

  .faq-category {
    padding: 8px 12px;
    font-size: 13px;
  }

  .accordion-header {
    padding: var(--spacing-sm);
    font-size: 15px;
  }

  .accordion-body {
    padding: var(--spacing-sm);
    font-size: 13px;
  }

  .accordion-item {
    margin-bottom: var(--spacing-sm);
  }
}
