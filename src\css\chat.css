/* ===== CHAT PAGE STYLES ===== */
/* Styles specific to the chat page */

.chat-container {
  min-height: 100vh;
  padding: 82px 0;
  max-width: 1272px;
  margin: 0 auto;
}

/* Chat Header */
.chat-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg) var(--spacing-md);
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 24px 24px 0 0;
  border: 1px solid #d2d6db;
  border-bottom: none;
}

.chat-back-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  border: 1px solid #d2d6db;
  border-radius: 8px;
  color: var(--text-color);
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.chat-back-btn:hover {
  background: #f8f9fa;
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateX(-2px);
}

.chat-back-btn i {
  font-size: 16px;
}

.chat-title {
  flex: 1;
}

.chat-title h2 {
  margin: 0 0 4px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  font-family: "Plus Jakarta Sans", sans-serif;
}

.chat-title p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.chat-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 260px);
  border: 1px solid #d2d6db;
  border-top: none;
  background: #ffffff;
  border-radius: 0 0 24px 24px;
  overflow: hidden;
  position: relative;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 24px;
}

.chat-messages-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 100%;
}

.chat-message {
  display: flex;
  width: 100%;
}

.chat-message.user-message {
  justify-content: flex-end;
}

.chat-message.ai-message {
  justify-content: flex-start;
}

.chat-bubble {
  max-width: 60%;
  padding: 8px 12px;
  border-radius: 8px;
  word-wrap: break-word;
}

.chat-bubble-grid {
  max-width: 100%;
  padding: 8px 12px;
  border-radius: 8px;
  word-wrap: break-word;
}

.chat-message.user-message .chat-bubble  {
  background: #5f9bfe;
  color: #ffffff;
}

.chat-message.ai-message .chat-bubble {
  background: #d2d6db;
  color: #40474f;
}

.chat-message.system .chat-bubble {
  background: #f4c790;
  color: #cc7914;
  border: 1px solid #eda145;
}

.chat-text {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* AI message formatting */
.chat-text p {
  margin: 0 0 8px 0;
}

.chat-text p:last-child {
  margin-bottom: 0;
}

.chat-text ul,
.chat-text ol {
  margin: 8px 0;
  padding-left: 20px;
}

.chat-text li {
  margin: 4px 0;
  line-height: 1.5;
}

.chat-text strong {
  font-weight: 600;
}

.chat-text em {
  font-style: italic;
}

.chat-image {
  max-width: 276px;
  max-height: 197px;
  border-radius: 8px;
  margin-bottom: 8px;
  object-fit: cover;
}

.chat-image-placeholder {
  max-width: 276px;
  height: 120px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  border: 2px dashed #ccc;
}

.chat-image-placeholder i {
  font-size: 24px;
  margin-bottom: 8px;
}

.chat-image-placeholder span {
  font-size: 12px;
}

.chat-input-area {
  padding: 0 26px;
  background: #ffffff;
  margin: 0px 0px 28px;
}

.chat-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 18px;
  border: 1px solid #d2d6db;
  border-radius: 12px;
  background: #ffffff;
}

.chat-input-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
}

.chat-input {
  width: 100%;
  border: none;
  outline: none;
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 18px;
  line-height: 1.6;
  color: #2d3339;
  background: transparent;
  resize: none;
}

.chat-input::placeholder {
  color: #bbc2c9;
}

.chat-input-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.chat-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.chat-btn-primary {
  background: #297afe;
  color: #ffffff;
}

.chat-btn-primary:hover:not(:disabled) {
  background: #1e5bb8;
  transform: scale(1.05);
}

.chat-btn-primary.active {
  background: #1e5bb8;
}

.chat-btn-primary:disabled {
  background: #bbc2c9;
  cursor: not-allowed;
}

.chat-btn-secondary {
  background: #297afe;
  color: #bbc2c9;
}

.chat-btn-secondary:hover {
  background: #e8f2ff;
  color: #297afe;
}

.chat-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.chat-loading-content {
  text-align: center;
  padding: 24px;
}

.chat-loading-spinner {
  font-size: 24px;
  color: #297afe;
  margin-bottom: 16px;
}

.chat-loading-text {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 16px;
  color: #40474f;
  margin: 0;
}

/* AI Thinking Animation */
.ai-thinking .thinking-bubble {
  background: #d2d6db;
  color: #40474f;
  padding: 12px 16px;
  border-radius: 8px;
  max-width: 200px;
}

.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-text {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-size: 14px;
  color: #40474f;
  font-style: italic;
}

/* Responsive Design for Chat */
@media (max-width: 768px) {
  .chat-container {
    margin: 0 16px;
  }

  .chat-header {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .chat-title h2 {
    font-size: 1.3rem;
  }

  .chat-title p {
    font-size: 0.85rem;
  }

  .chat-content {
    height: calc(100vh - 240px);
  }

  .chat-messages {
    padding: 16px;
    margin-bottom: 24px;
  }

  .chat-bubble {
    max-width: 85%;
  }

  .chat-input-area {
    padding: 0 16px;
  }

  .chat-input {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .chat-container {
    padding: 16px 0;
    margin: 0 8px;
  }

  .chat-header {
    padding: var(--spacing-sm) var(--spacing-md);
    gap: var(--spacing-sm);
  }

  .chat-back-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 13px;
  }

  .chat-back-btn span {
    display: none; /* Hide text on very small screens */
  }

  .chat-title h2 {
    font-size: 1.2rem;
  }

  .chat-title p {
    font-size: 0.8rem;
  }

  .chat-content {
    height: calc(100vh - 200px);
  }

  .chat-bubble {
    max-width: 90%;
  }

  .chat-image {
    max-width: 200px;
    max-height: 150px;
  }
}
