/* ===== CSS VARIABLES ===== */
/* CSS Custom Properties for Anevia */

:root {
  /* Color palette */
  --primary-color: #297afe; /* Deep blue */
  --secondary-color: #297afe; /* Dark blue */
  --accent-color: #7e57c2; /* Purple */
  --text-color: #333333;
  --light-text: #ffffff;
  --background-color: #f5f5f5;
  --card-bg: #ffffff;
  --error-color: #f44336;
  --success-color: #4caf50;

  /* Typography */
  --font-family: "Plus Jakarta Sans", sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Border radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

  /* Animation variables */
  --animation-duration: 0.6s;
  --animation-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
