/* PWA Styles - Progressive Web App UI Components */

/* PWA Update Notification */
.pwa-update-notification,
.pwa-install-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(26, 35, 126, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideInRight 0.3s ease-out;
  max-width: 350px;
  min-width: 300px;
}

.pwa-notification-content {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.pwa-notification-content i {
  font-size: 1.2rem;
  color: #42a5f5;
  flex-shrink: 0;
}

.pwa-notification-content span {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.4;
}

.pwa-update-btn,
.pwa-install-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
  flex-shrink: 0;
}

.pwa-update-btn:hover,
.pwa-install-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.pwa-dismiss-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.pwa-dismiss-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Network Status Indicator */
.network-status {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid;
  animation: slideInDown 0.3s ease-out;
  display: none;
}

.network-status.online {
  background: rgba(76, 175, 80, 0.9);
  color: white;
  border-color: rgba(76, 175, 80, 0.3);
}

.network-status.offline {
  background: rgba(244, 67, 54, 0.9);
  color: white;
  border-color: rgba(244, 67, 54, 0.3);
  display: block;
}

/* PWA Install Button */
.pwa-install-prompt {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(26, 35, 126, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideInUp 0.3s ease-out;
  display: none;
  max-width: 90vw;
  text-align: center;
}

.pwa-install-prompt.show {
  display: block;
}

.pwa-install-prompt h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.pwa-install-prompt p {
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.4;
}

.pwa-install-prompt .btn-group {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.pwa-install-prompt button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
}

.pwa-install-prompt .btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.pwa-install-prompt .btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.pwa-install-prompt .btn-secondary {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.3);
}

.pwa-install-prompt .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Cache Status Indicator */
.cache-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9998;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  backdrop-filter: blur(5px);
  display: none;
  transition: all 0.3s ease;
}

.cache-status.show {
  display: block;
}

.cache-status.caching {
  background: rgba(255, 193, 7, 0.9);
  color: #333;
}

.cache-status.cached {
  background: rgba(76, 175, 80, 0.9);
}

.cache-status.error {
  background: rgba(244, 67, 54, 0.9);
}

/* Offline Indicator */
.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%);
  color: white;
  text-align: center;
  padding: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  z-index: 10001;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.offline-indicator.show {
  transform: translateY(0);
}

.offline-indicator i {
  margin-right: 8px;
}

/* PWA Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translate(-50%, 100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

/* PWA Loading States */
.pwa-loading {
  position: relative;
  overflow: hidden;
}

.pwa-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive PWA Styles */
@media (max-width: 768px) {
  .pwa-update-notification,
  .pwa-install-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }

  .pwa-install-prompt {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
  }

  .pwa-install-prompt .btn-group {
    flex-direction: column;
  }

  .cache-status {
    bottom: 10px;
    right: 10px;
    font-size: 0.7rem;
  }
}

/* PWA Standalone Mode Styles */
@media (display-mode: standalone) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pwa-install-prompt {
    display: none !important;
  }
}

/* iOS PWA Styles */
@supports (-webkit-touch-callout: none) {
  .pwa-update-notification,
  .pwa-install-notification {
    backdrop-filter: none;
    background: #1a237e;
  }

  .network-status {
    backdrop-filter: none;
  }

  .pwa-install-prompt {
    backdrop-filter: none;
    background: #1a237e;
  }
}
