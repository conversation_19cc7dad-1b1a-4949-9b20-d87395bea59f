<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="120px" viewBox="0 0 200 120" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <!-- Simple eye outline -->
  <ellipse cx="100" cy="60" rx="70" ry="35" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.8" />

  <!-- Simple target circle for positioning -->
  <circle cx="100" cy="60" r="5" fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.9" />

  <!-- Simple crosshair -->
  <line x1="90" y1="60" x2="110" y2="60" stroke="#ffffff" stroke-width="1" opacity="0.7" />
  <line x1="100" y1="50" x2="100" y2="70" stroke="#ffffff" stroke-width="1" opacity="0.7" />

  <!-- Simple text instruction -->
  <text x="100" y="105" font-family="Plus Jakarta Sans, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle" opacity="0.9">Position eye here</text>
</svg>
